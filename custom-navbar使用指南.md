# Custom Navbar 组件使用指南

## 🎯 组件特性

### ✨ 灵活配置
- **搜索功能**: 可显示/隐藏，支持自定义图标和颜色
- **扫码功能**: 可显示/隐藏，支持自定义图标和颜色
- **布局模式**: 支持多种布局组合
- **样式定制**: 支持自定义颜色、背景等

### 📱 完美适配
- 自动适配小程序胶囊按钮
- 支持不同机型和平台
- 响应式设计

## 🔧 配置选项

### Props 参数

```typescript
interface Props {
  // 基础配置
  title?: string                    // 导航栏标题
  backgroundColor?: string          // 背景颜色
  
  // 搜索功能配置
  showSearch?: boolean             // 是否显示搜索功能
  searchPlaceholder?: string       // 搜索框占位符
  searchIcon?: string              // 搜索图标名称
  searchIconColor?: string         // 搜索图标颜色
  
  // 扫码功能配置
  showScan?: boolean              // 是否显示扫码功能
  scanIcon?: string               // 扫码图标名称
  scanIconColor?: string          // 扫码图标颜色
  
  // 布局配置
  showTitle?: boolean             // 是否显示标题
  centerContent?: 'search' | 'title' | 'custom'  // 中间区域显示内容
  
  // 样式配置
  titleColor?: string             // 标题颜色
  borderColor?: string            // 边框颜色
}
```

### 默认值

```typescript
{
  title: '雅韵珍品',
  backgroundColor: 'linear-gradient(135deg, rgba(27, 67, 50, 0.95), rgba(45, 106, 79, 0.95))',
  showSearch: false,              // 默认关闭
  showScan: false,               // 默认关闭
  showTitle: true,
  centerContent: 'title',
  searchPlaceholder: '搜索商品',
  searchIcon: 'search',
  searchIconColor: '#2D6A4F',
  scanIcon: 'scan',
  scanIconColor: '#FFFFFF'
}
```

## 📋 使用示例

### 1. 首页 - 搜索框居中

```vue
<template>
  <custom-navbar
    ref="navbarRef"
    title="雅韵珍品"
    :show-search="true"
    :show-scan="true"
    :show-title="true"
    center-content="search"
    search-placeholder="搜索沉香、玉石、珠宝"
    @search="handleSearch"
    @scan="handleScan"
  />
</template>
```

**效果**: 左侧显示标题，中间显示搜索框，右侧显示扫码按钮

### 2. 分类页 - 标题居中

```vue
<template>
  <custom-navbar
    ref="navbarRef"
    title="商品分类"
    :show-search="true"
    :show-scan="false"
    center-content="title"
    @search="handleSearch"
  />
</template>
```

**效果**: 中间显示标题，右侧显示搜索按钮，无扫码功能

### 3. 商品详情页 - 仅标题

```vue
<template>
  <custom-navbar
    ref="navbarRef"
    title="商品详情"
    :show-search="false"
    :show-scan="false"
    center-content="title"
  />
</template>
```

**效果**: 仅显示居中标题，无其他功能按钮

### 4. 搜索页 - 自定义样式

```vue
<template>
  <custom-navbar
    ref="navbarRef"
    title="搜索结果"
    :show-search="true"
    :show-scan="true"
    center-content="search"
    search-placeholder="输入关键词搜索"
    search-icon-color="#40916C"
    title-color="#1B4332"
    @search="handleSearch"
    @scan="handleScan"
  />
</template>
```

**效果**: 自定义颜色的搜索框和标题

### 5. 个人中心 - 简洁模式

```vue
<template>
  <custom-navbar
    ref="navbarRef"
    title="个人中心"
    :show-search="false"
    :show-scan="false"
    center-content="title"
    background-color="linear-gradient(135deg, rgba(111, 78, 55, 0.95), rgba(139, 105, 20, 0.95))"
  />
</template>
```

**效果**: 使用乌木色背景的简洁导航栏

## 🎨 布局模式

### centerContent 选项说明

#### 1. `centerContent="title"` (默认)
```
┌─────────────────────────────────┐
│        │     标题     │  🔍 📷  │
└─────────────────────────────────┘
```

#### 2. `centerContent="search"`
```
┌─────────────────────────────────┐
│  标题  │   🔍 搜索框   │   📷   │
└─────────────────────────────────┘
```

#### 3. `centerContent="custom"`
```
┌─────────────────────────────────┐
│  标题  │   自定义内容   │  🔍 📷  │
└─────────────────────────────────┘
```

## 🔧 高级用法

### 插槽自定义

```vue
<template>
  <custom-navbar ref="navbarRef">
    <!-- 自定义左侧内容 -->
    <template #left>
      <view class="custom-left">
        <wd-icon name="arrow-left" @tap="goBack" />
        <text>返回</text>
      </view>
    </template>
    
    <!-- 自定义中间内容 -->
    <template #center>
      <view class="custom-center">
        <text>自定义标题</text>
      </view>
    </template>
    
    <!-- 自定义右侧内容 -->
    <template #right>
      <view class="custom-right">
        <wd-icon name="more" @tap="showMore" />
      </view>
    </template>
  </custom-navbar>
</template>
```

### 动态配置

```vue
<script setup>
const navbarConfig = computed(() => {
  if (currentPage.value === 'home') {
    return {
      showSearch: true,
      showScan: true,
      centerContent: 'search'
    }
  } else if (currentPage.value === 'category') {
    return {
      showSearch: true,
      showScan: false,
      centerContent: 'title'
    }
  } else {
    return {
      showSearch: false,
      showScan: false,
      centerContent: 'title'
    }
  }
})
</script>

<template>
  <custom-navbar
    ref="navbarRef"
    v-bind="navbarConfig"
    @search="handleSearch"
    @scan="handleScan"
  />
</template>
```

## 📝 注意事项

1. **组件引用**: 确保正确设置 `ref` 并获取导航栏高度
2. **页面配置**: 页面需要设置 `navigationStyle: 'custom'`
3. **内容适配**: 页面内容需要设置正确的 `paddingTop`
4. **事件处理**: 根据需要监听 `@search` 和 `@scan` 事件

## 🚀 最佳实践

1. **首页**: 使用搜索框居中模式，提供搜索和扫码功能
2. **列表页**: 使用标题居中模式，提供搜索功能
3. **详情页**: 使用简洁模式，仅显示标题
4. **功能页**: 根据具体需求灵活配置

这样的设计让每个页面都能根据自己的需求灵活配置导航栏，既保持了设计的一致性，又提供了足够的灵活性。
