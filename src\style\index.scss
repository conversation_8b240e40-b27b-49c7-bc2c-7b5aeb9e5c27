// @import './iconfont.css';

// 中奢风格主题色配置 - 增强版
:root,
page {
  // 主题色 - 翡翠绿（增强版）
  --wot-color-theme: #1b4332;
  --wot-color-primary: #1b4332;

  // 按钮样式
  --wot-button-primary-bg-color: linear-gradient(135deg, #1b4332, #40916c);
  --wot-button-primary-border-color: #40916c;

  // 辅助色
  --wot-color-success: #40916c;
  --wot-color-warning: #8b6914;

  // 文字颜色
  --wot-color-title: #1b4332;
  --wot-color-content: #2d6a4f;
  --wot-color-tips: #6f4e37;

  // 背景色
  --wot-color-bg: #f8fbf8;
  --wot-color-bg-light: #ffffff;

  // 边框色
  --wot-color-border: rgba(27, 67, 50, 0.15);
  --wot-color-border-light: rgba(64, 145, 108, 0.1);
}

// 全局样式重置
page {
  background: linear-gradient(180deg, rgba(27, 67, 50, 0.02) 0%, #f8fbf8 50%, #ffffff 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #1b4332;
}

// 通用工具类 - 增强版
.luxury-gradient {
  background: linear-gradient(135deg, #1b4332, #40916c, #52b788);
}

.luxury-gradient-brown {
  background: linear-gradient(135deg, #6f4e37, #8b6914, #cd853f);
}

.luxury-shadow {
  box-shadow:
    0 6px 20px rgba(27, 67, 50, 0.12),
    0 3px 10px rgba(45, 106, 79, 0.08);
}

.luxury-shadow-hover {
  box-shadow:
    0 12px 32px rgba(27, 67, 50, 0.2),
    0 6px 20px rgba(45, 106, 79, 0.15);
}

.luxury-border {
  border: 1px solid rgba(64, 145, 108, 0.15);
}

.luxury-text-gradient {
  background: linear-gradient(135deg, #1b4332, #40916c);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
