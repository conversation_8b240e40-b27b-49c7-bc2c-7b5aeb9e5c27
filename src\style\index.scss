// @import './iconfont.css';

// 中奢风格主题色配置
:root,
page {
  // 主题色 - 翡翠绿
  --wot-color-theme: #2f4f2f;
  --wot-color-primary: #2f4f2f;

  // 按钮样式
  --wot-button-primary-bg-color: #2f4f2f;
  --wot-button-primary-border-color: #2f4f2f;

  // 辅助色
  --wot-color-success: #3cb371;
  --wot-color-warning: #8b7355;

  // 文字颜色
  --wot-color-title: #2c2c2c;
  --wot-color-content: #666666;
  --wot-color-tips: #999999;

  // 背景色
  --wot-color-bg: #fafafa;
  --wot-color-bg-light: #ffffff;

  // 边框色
  --wot-color-border: rgba(47, 79, 47, 0.1);
  --wot-color-border-light: rgba(47, 79, 47, 0.05);
}

// 全局样式重置
page {
  background-color: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

// 通用工具类
.luxury-gradient {
  background: linear-gradient(135deg, #2f4f2f, #3cb371);
}

.luxury-shadow {
  box-shadow: 0 4px 16px rgba(47, 79, 47, 0.08);
}

.luxury-shadow-hover {
  box-shadow: 0 8px 24px rgba(47, 79, 47, 0.15);
}
