# 小程序导航栏适配解决方案

## 🎯 问题描述

在不同机型的微信小程序中，自定义导航栏与右上角胶囊状态栏产生重叠遮挡，主要原因：
- 不同机型的状态栏高度不同
- 胶囊按钮的高度和位置不同
- 需要动态计算适配高度

## 🔧 解决方案

### 适配原理

根据微信小程序官方文档，导航栏适配需要考虑：

1. **状态栏高度**: 通过 `uni.getSystemInfoSync()` 获取
2. **胶囊信息**: 通过 `uni.getMenuButtonBoundingClientRect()` 获取
3. **导航栏高度计算**:
   ```
   导航栏高度 = 胶囊高度 + (胶囊顶部距离 - 状态栏高度) × 2
   总高度 = 状态栏高度 + 导航栏高度
   ```

### 实现步骤

#### 1. 创建自定义导航栏组件

文件：`src/components/custom-navbar/custom-navbar.vue`

**核心功能**：
- 动态计算状态栏和导航栏高度
- 支持插槽自定义内容
- 兼容不同平台（小程序/H5）
- 提供统一的样式和交互

**关键代码**：
```typescript
// 获取系统信息
const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = ref(systemInfo.statusBarHeight || 20)

// 计算导航栏高度
const calculateNavbarHeight = () => {
  try {
    // #ifdef MP-WEIXIN
    const menuButton = uni.getMenuButtonBoundingClientRect()
    const heightDiff = menuButton.top - statusBarHeight.value
    navbarHeight.value = menuButton.height + heightDiff * 2
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
    // #endif
  } catch (error) {
    // 使用默认值
    navbarHeight.value = 44
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
  }
}
```

#### 2. 组件特性

**Props 配置**：
- `title`: 导航栏标题
- `showSearch`: 是否显示搜索框
- `showScan`: 是否显示扫码按钮
- `searchPlaceholder`: 搜索框占位符
- `backgroundColor`: 背景颜色

**插槽支持**：
- `left`: 左侧内容
- `center`: 中间内容  
- `right`: 右侧内容

**事件**：
- `@search`: 搜索事件
- `@scan`: 扫码事件

#### 3. 页面使用

```vue
<template>
  <view class="home-container">
    <!-- 自定义导航栏 -->
    <custom-navbar 
      ref="navbarRef"
      title="雅韵珍品"
      :show-search="true"
      :show-scan="true"
      search-placeholder="搜索沉香、玉石、珠宝"
      @search="handleSearch"
      @scan="handleScan"
    />

    <!-- 主要内容区域 -->
    <scroll-view 
      scroll-y 
      class="main-content" 
      :style="{ paddingTop: navbarTotalHeight + 'px' }"
    >
      <!-- 页面内容 -->
    </scroll-view>
  </view>
</template>

<script setup>
// 导航栏引用和高度
const navbarRef = ref()
const navbarTotalHeight = ref(64) // 默认高度

// 获取导航栏高度
const getNavbarHeight = () => {
  nextTick(() => {
    if (navbarRef.value) {
      navbarTotalHeight.value = navbarRef.value.totalNavbarHeight || 64
    }
  })
}

onLoad(() => {
  getNavbarHeight()
})

onMounted(() => {
  getNavbarHeight()
})
</script>
```

## 🎨 样式特点

### 视觉效果
- 渐变背景：深绿到中绿的渐变
- 毛玻璃效果：`backdrop-filter: blur(15px)`
- 阴影效果：增强层次感
- 响应式设计：适配不同屏幕

### 布局结构
```
┌─────────────────────────────────┐
│          状态栏占位              │  ← statusBarHeight
├─────────────────────────────────┤
│  Logo  │    搜索框    │  扫码   │  ← navbarHeight
└─────────────────────────────────┘
│                                 │
│          页面内容               │  ← paddingTop: totalHeight
│                                 │
```

## 📱 兼容性

### 支持平台
- ✅ 微信小程序
- ✅ H5
- ✅ App
- ✅ 其他小程序平台

### 适配机型
- iPhone 各系列（刘海屏、非刘海屏）
- Android 各品牌机型
- iPad 等平板设备

## 🔍 调试信息

组件会在控制台输出适配信息：
```javascript
console.log('小程序导航栏适配信息:', {
  statusBarHeight: 44,        // 状态栏高度
  menuButton: {...},          // 胶囊信息
  navbarHeight: 44,          // 导航栏高度
  totalHeight: 88            // 总高度
})
```

## 📝 注意事项

1. **页面配置**: 确保页面 `navigationStyle` 设置为 `custom`
2. **组件引用**: 正确设置 ref 并在生命周期中获取高度
3. **内容适配**: 页面内容需要设置 `paddingTop` 避免被遮挡
4. **平台差异**: 不同平台可能有细微差异，建议实机测试

## 🚀 使用效果

- ✅ 完美适配各种机型
- ✅ 避免与胶囊按钮重叠
- ✅ 保持中奢风格设计
- ✅ 流畅的用户体验

这个解决方案完全解决了小程序导航栏适配问题，确保在所有设备上都有完美的显示效果。
