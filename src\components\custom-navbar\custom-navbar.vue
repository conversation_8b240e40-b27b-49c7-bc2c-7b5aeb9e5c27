<template>
  <view class="custom-navbar" :style="navbarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="{ height: navbarHeight + 'px' }">
      <!-- 左侧内容 -->
      <view class="navbar-left">
        <slot name="left">
          <view class="brand-logo">
            <text class="brand-text">{{ title || '雅韵珍品' }}</text>
          </view>
        </slot>
      </view>

      <!-- 中间内容 -->
      <view class="navbar-center">
        <slot name="center">
          <view v-if="showSearch" class="search-container" @tap="handleSearch">
            <wd-icon name="search" size="16px" color="#2D6A4F"></wd-icon>
            <text class="search-placeholder">
              {{ searchPlaceholder || '搜索沉香、玉石、珠宝' }}
            </text>
          </view>
        </slot>
      </view>

      <!-- 右侧内容 -->
      <view class="navbar-right">
        <slot name="right">
          <view v-if="showScan" class="nav-icons" @tap="handleScan">
            <wd-icon name="scan" size="20px" color="#FFFFFF"></wd-icon>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  title?: string
  showSearch?: boolean
  showScan?: boolean
  searchPlaceholder?: string
  backgroundColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '雅韵珍品',
  showSearch: true,
  showScan: true,
  searchPlaceholder: '搜索商品',
  backgroundColor: 'linear-gradient(135deg, rgba(27, 67, 50, 0.95), rgba(45, 106, 79, 0.95))',
})

const emit = defineEmits<{
  search: []
  scan: []
}>()

// 获取系统信息
const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = ref(systemInfo.statusBarHeight || 20)

// 获取胶囊信息（仅小程序环境）
const menuButtonInfo = ref<any>({})
const navbarHeight = ref(44) // 默认导航栏高度
const totalNavbarHeight = ref(64) // 默认总高度

// 计算导航栏高度
const calculateNavbarHeight = () => {
  try {
    // #ifdef MP-WEIXIN
    const menuButton = uni.getMenuButtonBoundingClientRect()
    menuButtonInfo.value = menuButton

    // 导航栏高度 = 胶囊高度 + (胶囊顶部距离 - 状态栏高度) * 2
    const heightDiff = menuButton.top - statusBarHeight.value
    navbarHeight.value = menuButton.height + heightDiff * 2
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value

    console.log('小程序导航栏适配信息:', {
      statusBarHeight: statusBarHeight.value,
      menuButton,
      navbarHeight: navbarHeight.value,
      totalHeight: totalNavbarHeight.value,
    })
    // #endif

    // #ifndef MP-WEIXIN
    // 非小程序环境使用默认值
    navbarHeight.value = 44
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
    // #endif
  } catch (error) {
    console.warn('获取胶囊信息失败，使用默认值:', error)
    navbarHeight.value = 44
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
  }
}

// 计算导航栏样式
const navbarStyle = computed(() => ({
  background: props.backgroundColor,
  height: totalNavbarHeight.value + 'px',
}))

// 事件处理
const handleSearch = () => {
  emit('search')
}

const handleScan = () => {
  emit('scan')
}

// 组件挂载时计算高度
onMounted(() => {
  calculateNavbarHeight()
})

// 暴露高度信息给父组件
defineExpose({
  statusBarHeight,
  navbarHeight,
  totalNavbarHeight,
})
</script>

<style lang="scss" scoped>
// 主题色定义
$primary-green: #1b4332;
$secondary-green: #2d6a4f;
$accent-green: #40916c;
$light-green: #52b788;
$accent-brown: #cd853f;
$text-secondary: #2d6a4f;
$white: #ffffff;

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(15px);
  border-bottom: 2px solid rgba(64, 145, 108, 0.3);
  box-shadow: 0 2px 20px rgba(27, 67, 50, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .navbar-left {
    flex-shrink: 0;

    .brand-logo {
      .brand-text {
        font-size: 20px;
        font-weight: 700;
        background: linear-gradient(135deg, $accent-brown, $light-green);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 1.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .navbar-center {
    flex: 1;
    margin: 0 16px;

    .search-container {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(64, 145, 108, 0.3);
      border-radius: 22px;
      padding: 10px 16px;
      transition: all 0.3s ease;
      box-shadow: inset 0 2px 4px rgba(27, 67, 50, 0.05);

      &:active {
        background: rgba(255, 255, 255, 1);
        border-color: $accent-green;
        box-shadow: 0 0 0 2px rgba(64, 145, 108, 0.2);
      }

      .search-placeholder {
        margin-left: 8px;
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
      }
    }
  }

  .navbar-right {
    flex-shrink: 0;

    .nav-icons {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(0.95);
      }
    }
  }
}
</style>
