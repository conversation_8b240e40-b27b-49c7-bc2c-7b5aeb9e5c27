<template>
  <view class="custom-navbar" :style="navbarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="{ height: navbarHeight + 'px' }">
      <!-- 左侧内容 -->
      <view class="navbar-left">
        <slot name="left">
          <!-- 当中间不显示标题时，左侧显示标题 -->
          <view v-if="showTitle && centerContent !== 'title'" class="brand-logo">
            <text class="brand-text" :style="titleColor ? { color: titleColor } : {}">
              {{ title }}
            </text>
          </view>
        </slot>
      </view>

      <!-- 中间内容 -->
      <view class="navbar-center">
        <slot name="center">
          <!-- 中间显示标题 -->
          <view v-if="showTitle && centerContent === 'title'" class="brand-logo center-title">
            <text class="brand-text" :style="titleColor ? { color: titleColor } : {}">
              {{ title }}
            </text>
          </view>

          <!-- 中间显示搜索框 -->
          <view
            v-else-if="showSearch && centerContent === 'search'"
            class="search-container"
            @tap="handleSearch"
          >
            <wd-icon :name="searchIcon" size="16px" :color="searchIconColor"></wd-icon>
            <text class="search-placeholder">{{ searchPlaceholder }}</text>
          </view>
        </slot>
      </view>

      <!-- 右侧内容 -->
      <view class="navbar-right">
        <slot name="right">
          <!-- 搜索按钮（当搜索框不在中间时） -->
          <view
            v-if="showSearch && centerContent !== 'search'"
            class="nav-icons search-icon"
            @tap="handleSearch"
          >
            <wd-icon :name="searchIcon" size="18px" :color="searchIconColor"></wd-icon>
          </view>

          <!-- 扫码按钮 -->
          <view v-if="showScan" class="nav-icons scan-icon" @tap="handleScan">
            <wd-icon :name="scanIcon" size="20px" :color="scanIconColor"></wd-icon>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  // 基础配置
  title?: string
  backgroundColor?: string

  // 搜索功能配置
  showSearch?: boolean
  searchPlaceholder?: string
  searchIcon?: string
  searchIconColor?: string

  // 扫码功能配置
  showScan?: boolean
  scanIcon?: string
  scanIconColor?: string

  // 布局配置
  showTitle?: boolean
  centerContent?: 'search' | 'title' | 'custom' // 中间区域显示内容

  // 样式配置
  titleColor?: string
  borderColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  // 基础配置
  title: '雅韵珍品',
  backgroundColor: 'linear-gradient(135deg, rgba(27, 67, 50, 0.95), rgba(45, 106, 79, 0.95))',

  // 搜索功能配置
  showSearch: false, // 默认关闭，让页面自行决定
  searchPlaceholder: '搜索商品',
  searchIcon: 'search',
  searchIconColor: '#2D6A4F',

  // 扫码功能配置
  showScan: false, // 默认关闭，让页面自行决定
  scanIcon: 'scan',
  scanIconColor: '#FFFFFF',

  // 布局配置
  showTitle: true,
  centerContent: 'title', // 默认中间显示标题

  // 样式配置
  titleColor: '',
  borderColor: 'rgba(64, 145, 108, 0.3)',
})

const emit = defineEmits<{
  search: []
  scan: []
}>()

// 获取系统信息
const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = ref(systemInfo.statusBarHeight || 20)

// 获取胶囊信息（仅小程序环境）
const menuButtonInfo = ref<any>({})
const navbarHeight = ref(44) // 默认导航栏高度
const totalNavbarHeight = ref(64) // 默认总高度

// 计算导航栏高度
const calculateNavbarHeight = () => {
  try {
    // #ifdef MP-WEIXIN
    const menuButton = uni.getMenuButtonBoundingClientRect()
    menuButtonInfo.value = menuButton

    // 导航栏高度 = 胶囊高度 + (胶囊顶部距离 - 状态栏高度) * 2
    const heightDiff = menuButton.top - statusBarHeight.value
    navbarHeight.value = menuButton.height + heightDiff * 2
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value

    console.log('小程序导航栏适配信息:', {
      statusBarHeight: statusBarHeight.value,
      menuButton,
      navbarHeight: navbarHeight.value,
      totalHeight: totalNavbarHeight.value,
    })
    // #endif

    // #ifndef MP-WEIXIN
    // 非小程序环境使用默认值
    navbarHeight.value = 44
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
    // #endif
  } catch (error) {
    console.warn('获取胶囊信息失败，使用默认值:', error)
    navbarHeight.value = 44
    totalNavbarHeight.value = statusBarHeight.value + navbarHeight.value
  }
}

// 计算导航栏样式
const navbarStyle = computed(() => ({
  background: props.backgroundColor,
  height: totalNavbarHeight.value + 'px',
}))

// 事件处理
const handleSearch = () => {
  emit('search')
}

const handleScan = () => {
  emit('scan')
}

// 组件挂载时计算高度
onMounted(() => {
  calculateNavbarHeight()
})

// 暴露高度信息给父组件
defineExpose({
  statusBarHeight,
  navbarHeight,
  totalNavbarHeight,
})
</script>

<style lang="scss" scoped>
// 主题色定义
$primary-green: #1b4332;
$secondary-green: #2d6a4f;
$accent-green: #40916c;
$light-green: #52b788;
$accent-brown: #cd853f;
$text-secondary: #2d6a4f;
$white: #ffffff;

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(15px);
  border-bottom: 2px solid rgba(64, 145, 108, 0.3);
  box-shadow: 0 2px 20px rgba(27, 67, 50, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .navbar-left {
    flex-shrink: 0;

    .brand-logo {
      .brand-text {
        font-size: 20px;
        font-weight: 700;
        background: linear-gradient(135deg, $accent-brown, $light-green);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 1.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .navbar-center {
    flex: 1;
    margin: 0 16px;
    display: flex;
    justify-content: center;
    align-items: center;

    .center-title {
      text-align: center;
    }

    .search-container {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(64, 145, 108, 0.3);
      border-radius: 22px;
      padding: 10px 16px;
      transition: all 0.3s ease;
      box-shadow: inset 0 2px 4px rgba(27, 67, 50, 0.05);
      width: 100%;

      &:active {
        background: rgba(255, 255, 255, 1);
        border-color: $accent-green;
        box-shadow: 0 0 0 2px rgba(64, 145, 108, 0.2);
      }

      .search-placeholder {
        margin-left: 8px;
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
      }
    }
  }

  .navbar-right {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 8px;

    .nav-icons {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(0.95);
      }

      &.search-icon {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(64, 145, 108, 0.3);

        &:active {
          background: rgba(255, 255, 255, 1);
          border-color: $accent-green;
        }
      }

      &.scan-icon {
        background: rgba(255, 255, 255, 0.15);

        &:active {
          background: rgba(255, 255, 255, 0.25);
        }
      }
    }
  }
}
</style>
