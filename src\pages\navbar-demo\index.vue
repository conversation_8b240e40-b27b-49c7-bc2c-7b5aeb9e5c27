<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '导航栏示例',
  },
}
</route>
<template>
  <view class="demo-container">
    <!-- 当前演示的导航栏 -->
    <custom-navbar
      ref="navbarRef"
      :title="currentConfig.title"
      :show-search="currentConfig.showSearch"
      :show-scan="currentConfig.showScan"
      :show-title="currentConfig.showTitle"
      :center-content="currentConfig.centerContent"
      :search-placeholder="currentConfig.searchPlaceholder"
      @search="handleSearch"
      @scan="handleScan"
    />

    <!-- 主要内容区域 -->
    <scroll-view 
      scroll-y 
      class="main-content" 
      :style="{ paddingTop: navbarTotalHeight + 'px' }"
    >
      <view class="demo-content">
        <view class="demo-title">导航栏配置演示</view>
        
        <!-- 配置选项 -->
        <view class="config-section">
          <view class="section-title">选择配置模式</view>
          <view class="config-buttons">
            <wd-button
              v-for="(config, key) in demoConfigs"
              :key="key"
              :type="currentMode === key ? 'primary' : 'default'"
              size="small"
              @tap="switchConfig(key)"
            >
              {{ config.name }}
            </wd-button>
          </view>
        </view>

        <!-- 当前配置信息 -->
        <view class="current-config">
          <view class="section-title">当前配置</view>
          <view class="config-info">
            <view class="config-item">
              <text class="label">标题:</text>
              <text class="value">{{ currentConfig.title }}</text>
            </view>
            <view class="config-item">
              <text class="label">显示搜索:</text>
              <text class="value">{{ currentConfig.showSearch ? '是' : '否' }}</text>
            </view>
            <view class="config-item">
              <text class="label">显示扫码:</text>
              <text class="value">{{ currentConfig.showScan ? '是' : '否' }}</text>
            </view>
            <view class="config-item">
              <text class="label">中间内容:</text>
              <text class="value">{{ centerContentMap[currentConfig.centerContent] }}</text>
            </view>
          </view>
        </view>

        <!-- 使用说明 -->
        <view class="usage-section">
          <view class="section-title">使用说明</view>
          <view class="usage-text">
            这个导航栏组件支持灵活配置，每个页面可以根据自己的需求：
          </view>
          <view class="usage-list">
            <text class="usage-item">• 控制搜索功能的显示/隐藏</text>
            <text class="usage-item">• 控制扫码功能的显示/隐藏</text>
            <text class="usage-item">• 选择中间区域显示内容</text>
            <text class="usage-item">• 自定义图标和颜色</text>
            <text class="usage-item">• 完美适配小程序胶囊</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'NavbarDemo',
})

// 导航栏引用和高度
const navbarRef = ref()
const navbarTotalHeight = ref(64)

// 获取导航栏高度
const getNavbarHeight = () => {
  nextTick(() => {
    if (navbarRef.value) {
      navbarTotalHeight.value = navbarRef.value.totalNavbarHeight || 64
    }
  })
}

// 当前模式
const currentMode = ref('home')

// 中间内容映射
const centerContentMap = {
  title: '标题',
  search: '搜索框',
  custom: '自定义'
}

// 演示配置
const demoConfigs = {
  home: {
    name: '首页模式',
    title: '雅韵珍品',
    showSearch: true,
    showScan: true,
    showTitle: true,
    centerContent: 'search',
    searchPlaceholder: '搜索沉香、玉石、珠宝'
  },
  category: {
    name: '分类模式',
    title: '商品分类',
    showSearch: true,
    showScan: false,
    showTitle: true,
    centerContent: 'title',
    searchPlaceholder: '搜索分类'
  },
  detail: {
    name: '详情模式',
    title: '商品详情',
    showSearch: false,
    showScan: false,
    showTitle: true,
    centerContent: 'title',
    searchPlaceholder: ''
  },
  search: {
    name: '搜索模式',
    title: '搜索结果',
    showSearch: true,
    showScan: true,
    showTitle: true,
    centerContent: 'search',
    searchPlaceholder: '输入关键词搜索'
  },
  profile: {
    name: '个人中心',
    title: '个人中心',
    showSearch: false,
    showScan: false,
    showTitle: true,
    centerContent: 'title',
    searchPlaceholder: ''
  }
}

// 当前配置
const currentConfig = computed(() => demoConfigs[currentMode.value])

// 切换配置
const switchConfig = (mode: string) => {
  currentMode.value = mode
}

// 事件处理
const handleSearch = () => {
  uni.showToast({
    title: '搜索功能',
    icon: 'none'
  })
}

const handleScan = () => {
  uni.showToast({
    title: '扫码功能',
    icon: 'none'
  })
}

onLoad(() => {
  getNavbarHeight()
})

onMounted(() => {
  getNavbarHeight()
})
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(180deg, rgba(27, 67, 50, 0.03) 0%, rgba(248, 251, 248, 0.8) 50%, #ffffff 100%);
}

.main-content {
  min-height: 100vh;
}

.demo-content {
  padding: 20px;
}

.demo-title {
  font-size: 24px;
  font-weight: 700;
  color: #1B4332;
  text-align: center;
  margin-bottom: 30px;
}

.config-section, .current-config, .usage-section {
  margin-bottom: 30px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(27, 67, 50, 0.08);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1B4332;
  margin-bottom: 16px;
}

.config-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.config-info {
  .config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(27, 67, 50, 0.1);

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 500;
      color: #2D6A4F;
    }

    .value {
      color: #1B4332;
      font-weight: 600;
    }
  }
}

.usage-text {
  color: #666666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.usage-list {
  .usage-item {
    display: block;
    color: #2D6A4F;
    line-height: 1.8;
    margin-bottom: 8px;
  }
}
</style>
