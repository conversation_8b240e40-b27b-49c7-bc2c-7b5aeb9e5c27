<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '雅韵珍品',
  },
}
</route>
<template>
  <view class="home-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="brand-logo">
          <text class="brand-text">雅韵珍品</text>
        </view>
        <view class="search-container" @tap="handleSearch">
          <wd-icon name="search" size="16px" color="#8B7355"></wd-icon>
          <text class="search-placeholder">搜索沉香、玉石、珠宝</text>
        </view>
        <view class="nav-icons">
          <wd-icon name="scan" size="20px" color="#2F4F2F" @tap="handleScan"></wd-icon>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view scroll-y class="main-content" enhanced :show-scrollbar="false">
      <!-- 轮播图 -->
      <view class="banner-section">
        <swiper
          class="banner-swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="4000"
          :duration="800"
          indicator-color="rgba(255,255,255,0.4)"
          indicator-active-color="#2F4F2F"
        >
          <swiper-item v-for="(banner, index) in bannerList" :key="index">
            <view class="banner-item" @tap="handleBannerTap(banner)">
              <image :src="banner.image" mode="aspectFill" class="banner-image"></image>
              <view class="banner-overlay">
                <view class="banner-title">{{ banner.title }}</view>
                <view class="banner-subtitle">{{ banner.subtitle }}</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 商品分类 -->
      <view class="category-section">
        <view class="section-title">
          <text class="title-text">臻品分类</text>
          <view class="title-decoration"></view>
        </view>
        <view class="category-grid">
          <view
            v-for="(category, index) in categoryList"
            :key="index"
            class="category-item"
            @tap="handleCategoryTap(category)"
          >
            <view class="category-icon-wrapper">
              <image :src="category.icon" mode="aspectFit" class="category-icon"></image>
            </view>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </view>

      <!-- 精选推荐 -->
      <view class="featured-section">
        <view class="section-title">
          <text class="title-text">精选推荐</text>
          <view class="title-decoration"></view>
        </view>
        <view class="featured-grid">
          <view
            v-for="(product, index) in featuredProducts"
            :key="index"
            class="product-card"
            @tap="handleProductTap(product)"
          >
            <view class="product-image-wrapper">
              <image :src="product.image" mode="aspectFill" class="product-image"></image>
              <view v-if="product.tag" class="product-tag">{{ product.tag }}</view>
            </view>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-desc">{{ product.description }}</text>
              <view class="product-price-wrapper">
                <text class="product-price">¥{{ product.price }}</text>
                <text v-if="product.originalPrice" class="original-price">
                  ¥{{ product.originalPrice }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 品牌故事 -->
      <view class="brand-story-section">
        <view class="story-card">
          <view class="story-content">
            <view class="story-title">传承千年工艺</view>
            <view class="story-subtitle">每一件珍品都承载着深厚的文化底蕴</view>
            <view class="story-description">
              我们精选来自世界各地的优质沉香、翡翠玉石和珠宝，
              秉承传统工艺与现代美学的完美融合，为您呈现独一无二的艺术珍品。
            </view>
            <wd-button
              type="primary"
              size="small"
              custom-class="story-button"
              @tap="handleLearnMore"
            >
              了解更多
            </wd-button>
          </view>
          <view class="story-image-wrapper">
            <image
              src="/static/images/brand-story.jpg"
              mode="aspectFill"
              class="story-image"
            ></image>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="bottom-spacing"></view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 轮播图数据
const bannerList = ref([
  {
    id: 1,
    image: '/static/images/banner1.jpg',
    title: '沉香雅韵',
    subtitle: '千年香韵，一品难求',
    link: '/pages/category/index?type=chenxiang',
  },
  {
    id: 2,
    image: '/static/images/banner2.jpg',
    title: '翡翠传奇',
    subtitle: '玉石之王，温润如君子',
    link: '/pages/category/index?type=jade',
  },
  {
    id: 3,
    image: '/static/images/banner3.jpg',
    title: '珠宝华章',
    subtitle: '璀璨夺目，彰显尊贵',
    link: '/pages/category/index?type=jewelry',
  },
])

// 分类数据
const categoryList = ref([
  { id: 1, name: '沉香', icon: '/static/images/category-chenxiang.png', type: 'chenxiang' },
  { id: 2, name: '翡翠', icon: '/static/images/category-jade.png', type: 'jade' },
  { id: 3, name: '和田玉', icon: '/static/images/category-hetianyu.png', type: 'hetianyu' },
  { id: 4, name: '珠宝', icon: '/static/images/category-jewelry.png', type: 'jewelry' },
  { id: 5, name: '手串', icon: '/static/images/category-bracelet.png', type: 'bracelet' },
  { id: 6, name: '摆件', icon: '/static/images/category-ornament.png', type: 'ornament' },
  { id: 7, name: '定制', icon: '/static/images/category-custom.png', type: 'custom' },
  { id: 8, name: '更多', icon: '/static/images/category-more.png', type: 'more' },
])

// 精选商品数据
const featuredProducts = ref([
  {
    id: 1,
    name: '奇楠沉香手串',
    description: '越南芽庄奇楠，香韵醇厚',
    price: '12800',
    originalPrice: '15800',
    image: '/static/images/product1.jpg',
    tag: '限量',
  },
  {
    id: 2,
    name: '冰种翡翠手镯',
    description: '缅甸A货翡翠，水头极佳',
    price: '28800',
    image: '/static/images/product2.jpg',
    tag: '精品',
  },
  {
    id: 3,
    name: '和田玉观音吊坠',
    description: '新疆和田白玉，温润细腻',
    price: '8800',
    originalPrice: '10800',
    image: '/static/images/product3.jpg',
  },
  {
    id: 4,
    name: '南红玛瑙手串',
    description: '保山南红，色泽艳丽',
    price: '3800',
    image: '/static/images/product4.jpg',
    tag: '热销',
  },
])

// 事件处理函数
const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

const handleScan = () => {
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果:', res)
    },
  })
}

const handleBannerTap = (banner: any) => {
  if (banner.link) {
    uni.navigateTo({
      url: banner.link,
    })
  }
}

const handleCategoryTap = (category: any) => {
  uni.navigateTo({
    url: `/pages/category/index?type=${category.type}`,
  })
}

const handleProductTap = (product: any) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`,
  })
}

const handleLearnMore = () => {
  uni.navigateTo({
    url: '/pages/about/brand',
  })
}

onLoad(() => {
  console.log('首页加载完成')
})
</script>

<style lang="scss" scoped>
// 主题色定义
$primary-green: #2f4f2f; // 翡翠绿（深绿）
$secondary-green: #3cb371; // 翡翠绿（中绿）
$accent-green: #90ee90; // 翡翠绿（浅绿）
$primary-brown: #8b4513; // 乌木色（深棕）
$secondary-brown: #8b7355; // 乌木色（中棕）
$accent-brown: #d2b48c; // 乌木色（浅棕）
$text-primary: #2c2c2c;
$text-secondary: #666666;
$text-light: #999999;
$background: #fafafa;
$white: #ffffff;

.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

// 自定义导航栏
.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(47, 79, 47, 0.1);

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    height: 44px;

    .brand-logo {
      .brand-text {
        font-size: 20px;
        font-weight: 600;
        color: $primary-green;
        letter-spacing: 1px;
      }
    }

    .search-container {
      flex: 1;
      display: flex;
      align-items: center;
      background: rgba(47, 79, 47, 0.05);
      border-radius: 20px;
      padding: 8px 16px;
      margin: 0 16px;
      transition: all 0.3s ease;

      &:active {
        background: rgba(47, 79, 47, 0.1);
      }

      .search-placeholder {
        margin-left: 8px;
        font-size: 14px;
        color: $secondary-brown;
      }
    }

    .nav-icons {
      display: flex;
      align-items: center;
    }
  }
}

// 主要内容区域
.main-content {
  height: calc(100vh - 44px);
}

// 轮播图区域
.banner-section {
  margin: 16px 20px 24px;

  .banner-swiper {
    height: 180px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(47, 79, 47, 0.15);

    .banner-item {
      position: relative;
      height: 100%;

      .banner-image {
        width: 100%;
        height: 100%;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        padding: 24px 20px 16px;
        color: $white;

        .banner-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .banner-subtitle {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }
  }
}

// 区块标题
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 20px;

  .title-text {
    font-size: 18px;
    font-weight: 600;
    color: $text-primary;
    margin-right: 12px;
  }

  .title-decoration {
    width: 24px;
    height: 3px;
    background: linear-gradient(90deg, $primary-green, $secondary-green);
    border-radius: 2px;
  }
}

// 分类区域
.category-section {
  margin-bottom: 32px;

  .category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    padding: 0 20px;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 8px;
      background: $white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(47, 79, 47, 0.08);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(47, 79, 47, 0.15);
      }

      .category-icon-wrapper {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(47, 79, 47, 0.1), rgba(60, 179, 113, 0.1));
        display: flex;
        align-items: center;
        justify-content: center;

        .category-icon {
          width: 24px;
          height: 24px;
        }
      }

      .category-name {
        font-size: 12px;
        color: $text-primary;
        font-weight: 500;
      }
    }
  }
}

// 精选推荐区域
.featured-section {
  margin-bottom: 32px;

  .featured-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 0 20px;

    .product-card {
      background: $white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(47, 79, 47, 0.08);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(47, 79, 47, 0.15);
      }

      .product-image-wrapper {
        position: relative;
        height: 140px;
        overflow: hidden;

        .product-image {
          width: 100%;
          height: 100%;
          transition: transform 0.3s ease;
        }

        .product-tag {
          position: absolute;
          top: 8px;
          left: 8px;
          background: linear-gradient(135deg, $primary-green, $secondary-green);
          color: $white;
          font-size: 10px;
          padding: 4px 8px;
          border-radius: 8px;
          font-weight: 500;
        }
      }

      .product-info {
        padding: 12px;

        .product-name {
          font-size: 14px;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-desc {
          font-size: 12px;
          color: $text-secondary;
          margin-bottom: 8px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-price-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;

          .product-price {
            font-size: 16px;
            font-weight: 600;
            color: $primary-green;
          }

          .original-price {
            font-size: 12px;
            color: $text-light;
            text-decoration: line-through;
          }
        }
      }
    }
  }
}

// 品牌故事区域
.brand-story-section {
  margin: 0 20px 32px;

  .story-card {
    background: linear-gradient(135deg, rgba(47, 79, 47, 0.05), rgba(60, 179, 113, 0.05));
    border-radius: 20px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    border: 1px solid rgba(47, 79, 47, 0.1);

    .story-content {
      flex: 1;

      .story-title {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 8px;
      }

      .story-subtitle {
        font-size: 14px;
        color: $secondary-brown;
        margin-bottom: 12px;
        font-weight: 500;
      }

      .story-description {
        font-size: 13px;
        color: $text-secondary;
        line-height: 1.6;
        margin-bottom: 16px;
      }

      :deep(.story-button) {
        background: linear-gradient(135deg, $primary-green, $secondary-green) !important;
        border: none !important;
        border-radius: 20px !important;
        font-size: 12px !important;
        padding: 8px 16px !important;
        height: auto !important;
      }
    }

    .story-image-wrapper {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      .story-image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 底部间距
.bottom-spacing {
  height: 20px;
}

// 响应式适配
@media (max-width: 375px) {
  .category-grid {
    gap: 12px !important;
  }

  .featured-grid {
    gap: 12px !important;
  }

  .story-card {
    flex-direction: column !important;
    text-align: center !important;

    .story-image-wrapper {
      order: -1;
    }
  }
}
</style>
