<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '雅韵珍品',
  },
}
</route>
<template>
  <view class="home-container">
    <!-- 自定义导航栏 -->
    <custom-navbar
      ref="navbarRef"
      title="雅韵珍品"
      :show-search="true"
      :show-scan="true"
      search-placeholder="搜索沉香、玉石、珠宝"
      @search="handleSearch"
      @scan="handleScan"
    />

    <!-- 主要内容区域 -->
    <scroll-view
      scroll-y
      class="main-content"
      enhanced
      :show-scrollbar="false"
      :style="{ paddingTop: navbarTotalHeight + 'px' }"
    >
      <!-- 轮播图 -->
      <view class="banner-section">
        <swiper
          class="banner-swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="4000"
          :duration="800"
          indicator-color="rgba(255,255,255,0.5)"
          indicator-active-color="#40916C"
        >
          <swiper-item v-for="(banner, index) in bannerList" :key="index">
            <view class="banner-item" @tap="handleBannerTap(banner)">
              <image :src="banner.image" mode="aspectFill" class="banner-image"></image>
              <view class="banner-overlay">
                <view class="banner-title">{{ banner.title }}</view>
                <view class="banner-subtitle">{{ banner.subtitle }}</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 商品分类 -->
      <view class="category-section">
        <view class="section-title">
          <text class="title-text">臻品分类</text>
          <view class="title-decoration"></view>
        </view>
        <view class="category-grid">
          <view
            v-for="(category, index) in categoryList"
            :key="index"
            class="category-item"
            @tap="handleCategoryTap(category)"
          >
            <view class="category-icon-wrapper">
              <image :src="category.icon" mode="aspectFit" class="category-icon"></image>
            </view>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </view>

      <!-- 精选推荐 -->
      <view class="featured-section">
        <view class="section-title">
          <text class="title-text">精选推荐</text>
          <view class="title-decoration"></view>
        </view>
        <view class="featured-grid">
          <view
            v-for="(product, index) in featuredProducts"
            :key="index"
            class="product-card"
            @tap="handleProductTap(product)"
          >
            <view class="product-image-wrapper">
              <image :src="product.image" mode="aspectFill" class="product-image"></image>
              <view v-if="product.tag" class="product-tag">{{ product.tag }}</view>
            </view>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-desc">{{ product.description }}</text>
              <view class="product-price-wrapper">
                <text class="product-price">¥{{ product.price }}</text>
                <text v-if="product.originalPrice" class="original-price">
                  ¥{{ product.originalPrice }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 品牌故事 -->
      <view class="brand-story-section">
        <view class="story-card">
          <view class="story-content">
            <view class="story-title">传承千年工艺</view>
            <view class="story-subtitle">每一件珍品都承载着深厚的文化底蕴</view>
            <view class="story-description">
              我们精选来自世界各地的优质沉香、翡翠玉石和珠宝，
              秉承传统工艺与现代美学的完美融合，为您呈现独一无二的艺术珍品。
            </view>
            <wd-button
              type="primary"
              size="small"
              custom-class="story-button"
              @tap="handleLearnMore"
            >
              了解更多
            </wd-button>
          </view>
          <view class="story-image-wrapper">
            <image
              src="/static/images/brand-story.jpg"
              mode="aspectFill"
              class="story-image"
            ></image>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="bottom-spacing"></view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'Home',
})

// 导航栏引用和高度
const navbarRef = ref()
const navbarTotalHeight = ref(64) // 默认高度

// 获取导航栏高度
const getNavbarHeight = () => {
  nextTick(() => {
    if (navbarRef.value) {
      navbarTotalHeight.value = navbarRef.value.totalNavbarHeight || 64
    }
  })
}

// 轮播图数据
const bannerList = ref([
  {
    id: 1,
    image: '/static/images/banner1.jpg',
    title: '沉香雅韵',
    subtitle: '千年香韵，一品难求',
    link: '/pages/category/index?type=chenxiang',
  },
  {
    id: 2,
    image: '/static/images/banner2.jpg',
    title: '翡翠传奇',
    subtitle: '玉石之王，温润如君子',
    link: '/pages/category/index?type=jade',
  },
  {
    id: 3,
    image: '/static/images/banner3.jpg',
    title: '珠宝华章',
    subtitle: '璀璨夺目，彰显尊贵',
    link: '/pages/category/index?type=jewelry',
  },
])

// 分类数据
const categoryList = ref([
  { id: 1, name: '沉香', icon: '/static/images/category-chenxiang.png', type: 'chenxiang' },
  { id: 2, name: '翡翠', icon: '/static/images/category-jade.png', type: 'jade' },
  { id: 3, name: '和田玉', icon: '/static/images/category-hetianyu.png', type: 'hetianyu' },
  { id: 4, name: '珠宝', icon: '/static/images/category-jewelry.png', type: 'jewelry' },
  { id: 5, name: '手串', icon: '/static/images/category-bracelet.png', type: 'bracelet' },
  { id: 6, name: '摆件', icon: '/static/images/category-ornament.png', type: 'ornament' },
  { id: 7, name: '定制', icon: '/static/images/category-custom.png', type: 'custom' },
  { id: 8, name: '更多', icon: '/static/images/category-more.png', type: 'more' },
])

// 精选商品数据
const featuredProducts = ref([
  {
    id: 1,
    name: '奇楠沉香手串',
    description: '越南芽庄奇楠，香韵醇厚',
    price: '12800',
    originalPrice: '15800',
    image: '/static/images/product1.jpg',
    tag: '限量',
  },
  {
    id: 2,
    name: '冰种翡翠手镯',
    description: '缅甸A货翡翠，水头极佳',
    price: '28800',
    image: '/static/images/product2.jpg',
    tag: '精品',
  },
  {
    id: 3,
    name: '和田玉观音吊坠',
    description: '新疆和田白玉，温润细腻',
    price: '8800',
    originalPrice: '10800',
    image: '/static/images/product3.jpg',
  },
  {
    id: 4,
    name: '南红玛瑙手串',
    description: '保山南红，色泽艳丽',
    price: '3800',
    image: '/static/images/product4.jpg',
    tag: '热销',
  },
])

// 事件处理函数
const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

const handleScan = () => {
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果:', res)
    },
  })
}

const handleBannerTap = (banner: any) => {
  if (banner.link) {
    uni.navigateTo({
      url: banner.link,
    })
  }
}

const handleCategoryTap = (category: any) => {
  uni.navigateTo({
    url: `/pages/category/index?type=${category.type}`,
  })
}

const handleProductTap = (product: any) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`,
  })
}

const handleLearnMore = () => {
  uni.navigateTo({
    url: '/pages/about/brand',
  })
}

onLoad(() => {
  console.log('首页加载完成')
  getNavbarHeight()
})

onMounted(() => {
  getNavbarHeight()
})
</script>

<style lang="scss" scoped>
// 主题色定义 - 增强版
$primary-green: #1b4332; // 翡翠绿（深绿）- 更深邃
$secondary-green: #2d6a4f; // 翡翠绿（中绿）- 更饱和
$accent-green: #40916c; // 翡翠绿（亮绿）- 更鲜明
$light-green: #52b788; // 翡翠绿（浅绿）- 新增
$primary-brown: #6f4e37; // 乌木色（深棕）- 更浓郁
$secondary-brown: #8b6914; // 乌木色（金棕）- 更奢华
$accent-brown: #cd853f; // 乌木色（浅棕）- 更温暖
$text-primary: #1b4332; // 使用深绿作为主文字色
$text-secondary: #2d6a4f; // 使用中绿作为次要文字色
$text-light: #6f4e37; // 使用棕色作为辅助文字色
$background: #f8fbf8; // 淡绿背景
$white: #ffffff;

.home-container {
  min-height: 100vh;
  background: linear-gradient(
    180deg,
    rgba(27, 67, 50, 0.03) 0%,
    rgba(248, 251, 248, 0.8) 50%,
    #ffffff 100%
  );
}

// 移除原有的自定义导航栏样式，现在使用组件

// 主要内容区域
.main-content {
  min-height: 100vh;
}

// 轮播图区域
.banner-section {
  margin: 16px 20px 24px;

  .banner-swiper {
    height: 200px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
      0 12px 32px rgba(27, 67, 50, 0.25),
      0 4px 16px rgba(45, 106, 79, 0.15);
    border: 2px solid rgba(64, 145, 108, 0.2);

    .banner-item {
      position: relative;
      height: 100%;

      .banner-image {
        width: 100%;
        height: 100%;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(27, 67, 50, 0.4), rgba(27, 67, 50, 0.8));
        padding: 32px 24px 20px;
        color: $white;

        .banner-title {
          font-size: 20px;
          font-weight: 700;
          margin-bottom: 6px;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          background: linear-gradient(135deg, #ffffff, rgba(205, 133, 63, 0.9));
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .banner-subtitle {
          font-size: 14px;
          opacity: 0.95;
          font-weight: 500;
          text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

// 区块标题
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;

  .title-text {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, $primary-green, $accent-green);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-right: 16px;
    text-shadow: 0 2px 4px rgba(27, 67, 50, 0.1);
  }

  .title-decoration {
    width: 32px;
    height: 4px;
    background: linear-gradient(90deg, $accent-green, $light-green, $accent-brown);
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(64, 145, 108, 0.3);
  }
}

// 分类区域
.category-section {
  margin-bottom: 36px;

  .category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 18px;
    padding: 0 20px;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 18px 10px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 251, 248, 0.8));
      border: 1px solid rgba(64, 145, 108, 0.15);
      border-radius: 16px;
      box-shadow:
        0 4px 16px rgba(27, 67, 50, 0.12),
        0 2px 8px rgba(45, 106, 79, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, $accent-green, $light-green);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      &:active {
        transform: translateY(-4px) scale(0.98);
        box-shadow:
          0 8px 24px rgba(27, 67, 50, 0.2),
          0 4px 16px rgba(45, 106, 79, 0.15);
        border-color: $accent-green;

        &::before {
          transform: scaleX(1);
        }
      }

      .category-icon-wrapper {
        width: 48px;
        height: 48px;
        margin-bottom: 10px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(27, 67, 50, 0.1), rgba(64, 145, 108, 0.15));
        border: 2px solid rgba(82, 183, 136, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .category-icon {
          width: 26px;
          height: 26px;
          filter: drop-shadow(0 2px 4px rgba(27, 67, 50, 0.1));
        }
      }

      .category-name {
        font-size: 13px;
        color: $text-primary;
        font-weight: 600;
        text-align: center;
      }

      &:active .category-icon-wrapper {
        background: linear-gradient(135deg, rgba(64, 145, 108, 0.2), rgba(82, 183, 136, 0.25));
        border-color: $accent-green;
        transform: scale(1.05);
      }
    }
  }
}

// 精选推荐区域
.featured-section {
  margin-bottom: 36px;

  .featured-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
    padding: 0 20px;

    .product-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 251, 248, 0.9));
      border: 1px solid rgba(64, 145, 108, 0.15);
      border-radius: 20px;
      overflow: hidden;
      box-shadow:
        0 6px 20px rgba(27, 67, 50, 0.12),
        0 3px 10px rgba(45, 106, 79, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(64, 145, 108, 0.05), rgba(82, 183, 136, 0.03));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
      }

      &:active {
        transform: translateY(-6px) scale(0.98);
        box-shadow:
          0 12px 32px rgba(27, 67, 50, 0.2),
          0 6px 20px rgba(45, 106, 79, 0.15);
        border-color: $accent-green;

        &::before {
          opacity: 1;
        }

        .product-image {
          transform: scale(1.05);
        }
      }

      .product-image-wrapper {
        position: relative;
        height: 160px;
        overflow: hidden;

        .product-image {
          width: 100%;
          height: 100%;
          transition: transform 0.4s ease;
          filter: brightness(1.02) contrast(1.05);
        }

        .product-tag {
          position: absolute;
          top: 10px;
          left: 10px;
          background: linear-gradient(135deg, $primary-green, $accent-green);
          color: $white;
          font-size: 11px;
          padding: 6px 10px;
          border-radius: 12px;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(27, 67, 50, 0.3);
          z-index: 2;
        }
      }

      .product-info {
        padding: 16px;
        position: relative;
        z-index: 2;

        .product-name {
          font-size: 15px;
          font-weight: 700;
          color: $text-primary;
          margin-bottom: 6px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.3;
        }

        .product-desc {
          font-size: 12px;
          color: $text-secondary;
          margin-bottom: 10px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 500;
        }

        .product-price-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;

          .product-price {
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(135deg, $primary-green, $accent-green);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .original-price {
            font-size: 13px;
            color: $text-light;
            text-decoration: line-through;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 品牌故事区域
.brand-story-section {
  margin: 0 20px 36px;

  .story-card {
    background: linear-gradient(
      135deg,
      rgba(27, 67, 50, 0.08),
      rgba(64, 145, 108, 0.06),
      rgba(82, 183, 136, 0.04)
    );
    border-radius: 24px;
    padding: 28px;
    display: flex;
    align-items: center;
    gap: 24px;
    border: 2px solid rgba(64, 145, 108, 0.2);
    box-shadow:
      0 8px 24px rgba(27, 67, 50, 0.15),
      0 4px 12px rgba(45, 106, 79, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, $accent-green, $light-green, $accent-brown);
    }

    .story-content {
      flex: 1;

      .story-title {
        font-size: 20px;
        font-weight: 700;
        background: linear-gradient(135deg, $primary-green, $accent-green);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 10px;
        line-height: 1.3;
      }

      .story-subtitle {
        font-size: 15px;
        color: $secondary-brown;
        margin-bottom: 14px;
        font-weight: 600;
        opacity: 0.9;
      }

      .story-description {
        font-size: 14px;
        color: $text-secondary;
        line-height: 1.7;
        margin-bottom: 18px;
        font-weight: 500;
      }

      :deep(.story-button) {
        background: linear-gradient(135deg, $primary-green, $accent-green) !important;
        border: none !important;
        border-radius: 24px !important;
        font-size: 13px !important;
        padding: 10px 20px !important;
        height: auto !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 12px rgba(27, 67, 50, 0.3) !important;
        transition: all 0.3s ease !important;

        &:active {
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 16px rgba(27, 67, 50, 0.4) !important;
        }
      }
    }

    .story-image-wrapper {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 3px solid rgba(64, 145, 108, 0.3);
      box-shadow: 0 4px 16px rgba(27, 67, 50, 0.2);

      .story-image {
        width: 100%;
        height: 100%;
        filter: brightness(1.05) contrast(1.1);
      }
    }
  }
}

// 底部间距
.bottom-spacing {
  height: 20px;
}

// 响应式适配
@media (max-width: 375px) {
  .category-grid {
    gap: 12px !important;
  }

  .featured-grid {
    gap: 12px !important;
  }

  .story-card {
    flex-direction: column !important;
    text-align: center !important;

    .story-image-wrapper {
      order: -1;
    }
  }
}
</style>
